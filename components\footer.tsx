import { Mail, Phone } from "lucide-react"
import Link from "next/link"

export default function Footer() {
  return (
    <footer className="bg-charcoal text-white">
      <div className="container mx-auto px-4 max-w-7xl py-16">
        <div className="grid lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-2">
            <Link href="/" className="flex items-center space-x-2 mb-6">
              <img
                src="/images/wellness-marketing-maestros-logo.png"
                alt="Wellness Marketing Maestros"
                className="h-8 w-auto"
              />
            </Link>
            <p className="text-cloud-grey mb-6 leading-relaxed max-w-md">
              Strategic consultancy for established wellness clinics, medical spas, and functional medicine practices.
              We architect enduring brand legacies and predictable patient growth.
            </p>
            <div className="space-y-2 text-cloud-grey">
              <div className="flex items-center">
                <Mail className="h-4 w-4 mr-2 text-strategy-blue-400" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center">
                <Phone className="h-4 w-4 mr-2 text-strategy-blue-400" />
                <span>Monday - Friday, 9:00 AM - 5:00 PM ET</span>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="font-semibold text-lg mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/" className="text-cloud-grey hover:text-strategy-blue transition-colors">
                  Home
                </Link>
              </li>
              <li>
                <Link href="/about" className="text-cloud-grey hover:text-strategy-blue transition-colors">
                  About Us
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-cloud-grey hover:text-strategy-blue transition-colors">
                  Contact
                </Link>
              </li>
            </ul>
          </div>

          {/* Services */}
          <div>
            <h3 className="font-semibold text-lg mb-4">Services</h3>
            <ul className="space-y-2 text-cloud-grey">
              <li>Growth Audit & Strategic Roadmap</li>
              <li>Core Implementation Projects</li>
              <li>Patient Acquisition Analysis</li>
              <li>Strategic Consultation</li>
            </ul>
          </div>
        </div>

        <div className="border-t border-charcoal-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-cloud-grey-400 text-sm">
            © {new Date().getFullYear()} Wellness Marketing Maestros. All rights reserved.
          </p>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <Link href="#" className="text-cloud-grey-400 hover:text-strategy-blue text-sm transition-colors">
              Privacy Policy
            </Link>
            <Link href="#" className="text-cloud-grey-400 hover:text-strategy-blue text-sm transition-colors">
              Terms of Service
            </Link>
          </div>
        </div>
      </div>
    </footer>
  )
}
