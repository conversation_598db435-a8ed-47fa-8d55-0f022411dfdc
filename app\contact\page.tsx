import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Mail, Phone, MapPin, Clock, ArrowRight } from "lucide-react"

export default function ContactPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-slate-50 to-white py-20 lg:py-32">
        <div className="container mx-auto px-4 max-w-7xl">
          <div className="max-w-4xl mx-auto text-center">
            <Badge variant="outline" className="mb-6 text-emerald-700 border-emerald-200 bg-emerald-50">
              Begin a Strategic Partnership
            </Badge>
            <h1 className="text-4xl lg:text-6xl font-bold text-slate-900 mb-8 leading-tight">
              Let's Begin the Conversation
            </h1>
          </div>
        </div>
      </section>

      {/* First Step Section */}
      <section className="py-20">
        <div className="container mx-auto px-4 max-w-7xl">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-6">
              We Look Forward to Learning About Your Vision.
            </h2>
            <p className="text-xl text-slate-600 mb-8 leading-relaxed">
              Reaching out to us is the first step in a strategic discussion about the future of your practice. We are
              here to answer your questions and determine if a partnership is the right fit to achieve your ambitious
              goals.
            </p>
            <div className="flex items-center justify-center space-x-6 text-slate-600">
              <div className="flex items-center">
                <Clock className="h-5 w-5 mr-2 text-emerald-600" />
                <span>Monday - Friday, 9:00 AM - 5:00 PM ET</span>
              </div>
              <div className="flex items-center">
                <ArrowRight className="h-5 w-5 mr-2 text-emerald-600" />
                <span>Response within 1 business day</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="py-20 bg-slate-50">
        <div className="container mx-auto px-4 max-w-7xl">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-4">Send Us a Message</h2>
              <p className="text-xl text-slate-600 leading-relaxed">
                This form is the most efficient way to begin our conversation. The details you provide will allow us to
                direct your inquiry to the appropriate senior strategist for a prompt and productive response.
              </p>
            </div>

            <Card className="p-8 bg-white border-2">
              <CardContent className="p-0">
                <form className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="firstName" className="text-slate-700 font-medium">
                        First Name
                      </Label>
                      <Input id="firstName" placeholder="Enter your first name" className="border-slate-300" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="lastName" className="text-slate-700 font-medium">
                        Last Name
                      </Label>
                      <Input id="lastName" placeholder="Enter your last name" className="border-slate-300" />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email" className="text-slate-700 font-medium">
                      Work Email
                    </Label>
                    <Input id="email" type="email" placeholder="Enter your work email" className="border-slate-300" />
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="company" className="text-slate-700 font-medium">
                        Practice / Company Name
                      </Label>
                      <Input id="company" placeholder="Enter your practice name" className="border-slate-300" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="website" className="text-slate-700 font-medium">
                        Website URL
                      </Label>
                      <Input id="website" placeholder="https://yourwebsite.com" className="border-slate-300" />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="interest" className="text-slate-700 font-medium">
                      I am interested in:
                    </Label>
                    <Select>
                      <SelectTrigger className="border-slate-300">
                        <SelectValue placeholder="Select your primary interest" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="growth-audit">Inquiring about a Growth Audit & Strategic Roadmap</SelectItem>
                        <SelectItem value="opportunity-analysis">
                          Requesting a Complimentary Patient Acquisition Opportunity Analysis
                        </SelectItem>
                        <SelectItem value="press-speaking">A Press or Speaking Engagement Inquiry</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="message" className="text-slate-700 font-medium">
                      Message
                    </Label>
                    <Textarea
                      id="message"
                      placeholder="Please provide a brief overview of your practice and your primary growth objective."
                      className="border-slate-300 min-h-[120px]"
                    />
                  </div>

                  <Button size="lg" className="w-full bg-emerald-600 hover:bg-emerald-700 text-white py-4 text-lg">
                    Begin the Conversation
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Alternative Contact Methods */}
      <section className="py-20">
        <div className="container mx-auto px-4 max-w-7xl">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-12 text-center">Other Ways to Connect</h2>

            <div className="grid lg:grid-cols-3 gap-8">
              <Card className="p-8 text-center border-2 hover:border-emerald-200 transition-colors">
                <CardContent className="p-0">
                  <div className="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Mail className="h-8 w-8 text-emerald-600" />
                  </div>
                  <h3 className="text-xl font-bold text-slate-900 mb-4">Email Our Partnership Team</h3>
                  <p className="text-slate-600 mb-4">
                    For direct inquiries, you can reach our strategic partnerships desk at:
                  </p>
                  <a
                    href="mailto:<EMAIL>"
                    className="text-emerald-600 font-medium hover:underline"
                  >
                    <EMAIL>
                  </a>
                </CardContent>
              </Card>

              <Card className="p-8 text-center border-2 hover:border-emerald-200 transition-colors">
                <CardContent className="p-0">
                  <div className="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Phone className="h-8 w-8 text-emerald-600" />
                  </div>
                  <h3 className="text-xl font-bold text-slate-900 mb-4">For Scheduled Calls</h3>
                  <p className="text-slate-600 mb-4">
                    To ensure every prospective partner receives our undivided attention, we conduct initial discussions
                    by appointment. Please use the form above to schedule a discovery call with one of our strategists.
                  </p>
                  <p className="text-slate-600 font-medium">[Phone Number Placeholder]</p>
                </CardContent>
              </Card>

              <Card className="p-8 text-center border-2 hover:border-emerald-200 transition-colors">
                <CardContent className="p-0">
                  <div className="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <MapPin className="h-8 w-8 text-emerald-600" />
                  </div>
                  <h3 className="text-xl font-bold text-slate-900 mb-4">Our Headquarters</h3>
                  <div className="text-slate-600 space-y-1">
                    <p>[Street Address]</p>
                    <p>[City, State, ZIP Code]</p>
                    <p>[Country]</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* What to Expect Next */}
      <section className="py-20 bg-slate-50">
        <div className="container mx-auto px-4 max-w-7xl">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-6 text-center">
              A Different Kind of Follow-Up
            </h2>
            <div className="bg-white rounded-lg p-8 border-2">
              <p className="text-xl text-slate-600 mb-6 leading-relaxed">
                Once you submit your inquiry, you will not be added to a generic mailing list or contacted by a junior
                salesperson.
              </p>
              <p className="text-lg text-slate-600 leading-relaxed">
                Your submission will be personally reviewed by a senior strategist. They will reach out to you within
                one business day to either answer your questions directly or schedule a complimentary, 30-minute
                introductory call to explore a potential partnership in more detail.
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
