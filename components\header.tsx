"use client"

import { useState, useRef } from "react"
import Link from "next/link"
import { ChevronDown } from "lucide-react"

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
    setActiveDropdown(null)
  }

  const toggleDropdown = (dropdown: string) => {
    setActiveDropdown(activeDropdown === dropdown ? null : dropdown)
  }

  const handleMouseEnter = (dropdown: string) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    setActiveDropdown(dropdown)
  }

  const handleMouseLeave = () => {
    timeoutRef.current = setTimeout(() => {
      setActiveDropdown(null)
    }, 150) // 150ms delay before closing
  }

  return (
    <header className="bg-white border-b border-cloud-grey sticky top-0 z-50 shadow-sm">
      <div className="container mx-auto px-4 max-w-7xl">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-3">
            <img
              src="/images/wellness-marketing-maestros-logo.png"
              alt="Wellness Marketing Maestros"
              className="h-10 w-auto"
              loading="eager"
            />
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {/* Who We Serve Dropdown */}
            <div
              className="relative group"
              onMouseEnter={() => handleMouseEnter("who-we-serve")}
              onMouseLeave={handleMouseLeave}
            >
              <button
                className="flex items-center space-x-1 text-charcoal-700 hover:text-strategy-blue transition-colors font-medium"
              >
                <span>Who We Serve</span>
                <ChevronDown className="h-4 w-4" />
              </button>
              {activeDropdown === "who-we-serve" && (
                <div
                  className="absolute top-full left-0 mt-2 w-64 bg-white border border-cloud-grey rounded-lg shadow-xl py-2 z-50"
                >
                  <Link
                    href="/wellness-clinics"
                    className="block px-4 py-3 text-charcoal-700 hover:bg-strategy-blue-50 hover:text-strategy-blue transition-colors"
                  >
                    <div className="font-medium">Wellness Clinics</div>
                    <div className="text-sm text-charcoal-500">Predictable patient acquisition</div>
                  </Link>
                  <Link
                    href="/medical-spas"
                    className="block px-4 py-3 text-charcoal-700 hover:bg-strategy-blue-50 hover:text-strategy-blue transition-colors"
                  >
                    <div className="font-medium">Medical Spas</div>
                    <div className="text-sm text-charcoal-500">Premium brand positioning</div>
                  </Link>
                  <Link
                    href="/holistic-functional-medicine"
                    className="block px-4 py-3 text-charcoal-700 hover:bg-strategy-blue-50 hover:text-strategy-blue transition-colors"
                  >
                    <div className="font-medium">Holistic & Functional Medicine</div>
                    <div className="text-sm text-charcoal-500">Authority platform development</div>
                  </Link>
                  <Link
                    href="/multi-practitioner-centers"
                    className="block px-4 py-3 text-charcoal-700 hover:bg-strategy-blue-50 hover:text-strategy-blue transition-colors"
                  >
                    <div className="font-medium">Multi-Practitioner Centers</div>
                    <div className="text-sm text-charcoal-500">Unified brand strategy</div>
                  </Link>
                </div>
              )}
            </div>

            {/* Our Approach Dropdown */}
            <div
              className="relative group"
              onMouseEnter={() => handleMouseEnter("our-approach")}
              onMouseLeave={handleMouseLeave}
            >
              <button
                className="flex items-center space-x-1 text-charcoal-700 hover:text-strategy-blue transition-colors font-medium"
              >
                <span>Our Approach</span>
                <ChevronDown className="h-4 w-4" />
              </button>
              {activeDropdown === "our-approach" && (
                <div
                  className="absolute top-full left-0 mt-2 w-72 bg-white border border-cloud-grey rounded-lg shadow-xl py-2 z-50"
                >
                  <Link
                    href="/growth-audit"
                    className="block px-4 py-3 text-charcoal-700 hover:bg-strategy-blue-50 hover:text-strategy-blue transition-colors"
                  >
                    <div className="font-medium">Growth Audit & Strategic Roadmap</div>
                    <div className="text-sm text-charcoal-500">Deep-dive diagnostic and strategic plan</div>
                  </Link>
                  <Link
                    href="/core-implementation"
                    className="block px-4 py-3 text-charcoal-700 hover:bg-strategy-blue-50 hover:text-strategy-blue transition-colors"
                  >
                    <div className="font-medium">Core Implementation Projects</div>
                    <div className="text-sm text-charcoal-500">Multi-month strategic execution</div>
                  </Link>
                </div>
              )}
            </div>

            <Link href="/about" className="text-charcoal-700 hover:text-strategy-blue transition-colors font-medium">
              About Us
            </Link>

            <Link
              href="/contact"
              className="bg-insight-gold hover:bg-insight-gold-600 text-charcoal px-6 py-2 rounded-lg transition-all duration-200 font-semibold shadow-md hover:shadow-lg"
            >
              Get Started
            </Link>
          </nav>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden flex items-center justify-center w-10 h-10 rounded-lg hover:bg-cloud-grey-100 transition-colors"
            onClick={toggleMenu}
            aria-label="Toggle mobile menu"
          >
            <div className="w-6 h-6 flex flex-col justify-center items-center">
              <span
                className={`bg-charcoal-700 block transition-all duration-300 ease-out h-0.5 w-6 rounded-sm ${isMenuOpen ? "rotate-45 translate-y-1" : "-translate-y-0.5"}`}
              ></span>
              <span
                className={`bg-charcoal-700 block transition-all duration-300 ease-out h-0.5 w-6 rounded-sm my-0.5 ${isMenuOpen ? "opacity-0" : "opacity-100"}`}
              ></span>
              <span
                className={`bg-charcoal-700 block transition-all duration-300 ease-out h-0.5 w-6 rounded-sm ${isMenuOpen ? "-rotate-45 -translate-y-1" : "translate-y-0.5"}`}
              ></span>
            </div>
          </button>

          {/* Mobile Menu */}
          {isMenuOpen && (
            <div className="md:hidden absolute top-16 left-0 right-0 bg-white border-b border-cloud-grey shadow-xl">
              <div className="container mx-auto px-4 py-6 space-y-4">
                <div>
                  <button
                    className="flex items-center justify-between w-full text-left text-charcoal-700 hover:text-strategy-blue transition-colors py-3 font-medium"
                    onClick={() => toggleDropdown("mobile-who-we-serve")}
                  >
                    <span>Who We Serve</span>
                    <ChevronDown
                      className={`h-4 w-4 transition-transform ${activeDropdown === "mobile-who-we-serve" ? "rotate-180" : ""}`}
                    />
                  </button>
                  {activeDropdown === "mobile-who-we-serve" && (
                    <div className="pl-4 space-y-3 mt-3 border-l-2 border-strategy-blue-100">
                      <Link
                        href="/wellness-clinics"
                        className="block text-charcoal-600 hover:text-strategy-blue transition-colors py-2"
                        onClick={toggleMenu}
                      >
                        Wellness Clinics
                      </Link>
                      <Link
                        href="/medical-spas"
                        className="block text-charcoal-600 hover:text-strategy-blue transition-colors py-2"
                        onClick={toggleMenu}
                      >
                        Medical Spas
                      </Link>
                      <Link
                        href="/holistic-functional-medicine"
                        className="block text-charcoal-600 hover:text-strategy-blue transition-colors py-2"
                        onClick={toggleMenu}
                      >
                        Holistic & Functional Medicine
                      </Link>
                      <Link
                        href="/multi-practitioner-centers"
                        className="block text-charcoal-600 hover:text-strategy-blue transition-colors py-2"
                        onClick={toggleMenu}
                      >
                        Multi-Practitioner Centers
                      </Link>
                    </div>
                  )}
                </div>

                <div>
                  <button
                    className="flex items-center justify-between w-full text-left text-charcoal-700 hover:text-strategy-blue transition-colors py-3 font-medium"
                    onClick={() => toggleDropdown("mobile-our-approach")}
                  >
                    <span>Our Approach</span>
                    <ChevronDown
                      className={`h-4 w-4 transition-transform ${activeDropdown === "mobile-our-approach" ? "rotate-180" : ""}`}
                    />
                  </button>
                  {activeDropdown === "mobile-our-approach" && (
                    <div className="pl-4 space-y-3 mt-3 border-l-2 border-strategy-blue-100">
                      <Link
                        href="/growth-audit"
                        className="block text-charcoal-600 hover:text-strategy-blue transition-colors py-2"
                        onClick={toggleMenu}
                      >
                        Growth Audit & Strategic Roadmap
                      </Link>
                      <Link
                        href="/core-implementation"
                        className="block text-charcoal-600 hover:text-strategy-blue transition-colors py-2"
                        onClick={toggleMenu}
                      >
                        Core Implementation Projects
                      </Link>
                    </div>
                  )}
                </div>

                <Link
                  href="/about"
                  className="block text-charcoal-700 hover:text-strategy-blue transition-colors py-3 font-medium"
                  onClick={toggleMenu}
                >
                  About Us
                </Link>

                <Link
                  href="/contact"
                  className="block bg-insight-gold hover:bg-insight-gold-600 text-charcoal px-6 py-3 rounded-lg transition-all duration-200 text-center font-semibold shadow-md"
                  onClick={toggleMenu}
                >
                  Get Started
                </Link>
              </div>
            </div>
          )}
        </div>
      </div>
    </header>
  )
}
