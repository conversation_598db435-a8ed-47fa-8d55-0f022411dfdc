@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Brand Color Palette - Wellness Marketing Maestros */

  /* Primary Colors (25% usage) */
  --strategy-blue: 89 113 232; /* #5971E8 */
  --charcoal: 51 51 51; /* #333333 */

  /* Secondary Colors */
  --insight-gold: 232 185 90; /* #E8B95A - Accent (5% usage) */
  --cloud-grey: 234 234 234; /* #EAEAEA */
  --background-white: 251 251 251; /* #FBFBFB - Primary neutral (70% usage) */
  --white: 255 255 255; /* #FFFFFF */

  /* Semantic color mappings using brand palette */
  --background: var(--background-white);
  --foreground: var(--charcoal);
  --card: var(--white);
  --card-foreground: var(--charcoal);
  --popover: var(--white);
  --popover-foreground: var(--charcoal);
  --primary: var(--strategy-blue);
  --primary-foreground: var(--white);
  --secondary: var(--cloud-grey);
  --secondary-foreground: var(--charcoal);
  --muted: var(--cloud-grey);
  --muted-foreground: 102 102 102; /* Lighter charcoal for muted text */
  --accent: var(--insight-gold);
  --accent-foreground: var(--charcoal);
  --destructive: 239 68 68; /* Red for errors */
  --destructive-foreground: var(--white);
  --border: var(--cloud-grey);
  --input: var(--cloud-grey);
  --ring: var(--strategy-blue);
  --chart-1: var(--strategy-blue);
  --chart-2: var(--insight-gold);
  --chart-3: var(--charcoal);
  --chart-4: var(--cloud-grey);
  --chart-5: 168 168 168; /* Medium grey */
  --radius: 0.625rem;
  --sidebar: var(--background-white);
  --sidebar-foreground: var(--charcoal);
  --sidebar-primary: var(--strategy-blue);
  --sidebar-primary-foreground: var(--white);
  --sidebar-accent: var(--cloud-grey);
  --sidebar-accent-foreground: var(--charcoal);
  --sidebar-border: var(--cloud-grey);
  --sidebar-ring: var(--strategy-blue);
}

.dark {
  /* Dark mode using brand colors with appropriate contrast */
  --background: var(--charcoal);
  --foreground: var(--white);
  --card: 26 26 26; /* Darker charcoal for cards */
  --card-foreground: var(--white);
  --popover: 26 26 26;
  --popover-foreground: var(--white);
  --primary: var(--strategy-blue);
  --primary-foreground: var(--white);
  --secondary: 64 64 64; /* Lighter charcoal for secondary */
  --secondary-foreground: var(--white);
  --muted: 64 64 64;
  --muted-foreground: 168 168 168; /* Medium grey for muted text */
  --accent: var(--insight-gold);
  --accent-foreground: var(--charcoal);
  --destructive: 239 68 68;
  --destructive-foreground: var(--white);
  --border: 64 64 64;
  --input: 64 64 64;
  --ring: var(--strategy-blue);
  --chart-1: var(--strategy-blue);
  --chart-2: var(--insight-gold);
  --chart-3: var(--white);
  --chart-4: 168 168 168;
  --chart-5: 128 128 128;
  --sidebar: var(--charcoal);
  --sidebar-foreground: var(--white);
  --sidebar-primary: var(--strategy-blue);
  --sidebar-primary-foreground: var(--white);
  --sidebar-accent: 64 64 64;
  --sidebar-accent-foreground: var(--white);
  --sidebar-border: 64 64 64;
  --sidebar-ring: var(--strategy-blue);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    line-height: 1.2;
    font-weight: 700;
  }
  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl font-bold text-charcoal;
    letter-spacing: -0.02em;
  }
  h2 {
    @apply text-2xl md:text-3xl lg:text-4xl font-bold text-charcoal;
    letter-spacing: -0.01em;
  }
  h3 {
    @apply text-xl md:text-2xl lg:text-3xl font-bold text-charcoal;
    letter-spacing: -0.01em;
  }
  h4 {
    @apply text-lg md:text-xl lg:text-2xl font-semibold text-charcoal;
  }
  h5 {
    @apply text-base md:text-lg lg:text-xl font-semibold text-charcoal;
  }
  h6 {
    @apply text-sm md:text-base lg:text-lg font-semibold text-charcoal;
  }
}

@layer components {
  /* Improved spacing utilities */
  .section-padding {
    @apply py-12 md:py-16 lg:py-20;
  }

  .section-padding-sm {
    @apply py-8 md:py-12 lg:py-16;
  }

  .content-spacing {
    @apply space-y-4 md:space-y-6;
  }

  /* Better mobile spacing */
  .mobile-spacing {
    @apply px-4 md:px-6 lg:px-8;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}
